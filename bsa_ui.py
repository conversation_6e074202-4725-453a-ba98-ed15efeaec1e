"""
BSA数据生成器 - Streamlit用户界面模块

包含Web界面、用户交互、界面布局等UI功能
"""

from datetime import datetime

import pandas as pd
import streamlit as st

from bsa_core import BSAConfigManager, BSADataEngine, BSAExportClient, BSACookieManager


class BSAUserInterface:
    """BSA用户界面管理类"""

    @staticmethod
    def _update_config(key: str, value):
        """更新配置项并立即保存"""
        if 'saved_config' not in st.session_state:
            st.session_state.saved_config = BSAConfigManager.load_config()

        # 更新session state
        st.session_state.saved_config[key] = value

        # 立即保存到文件
        BSAConfigManager.save_config(st.session_state.saved_config)

    @staticmethod
    def setup_page_config():
        """设置页面配置"""
        st.set_page_config(
            page_title="BSA 数据生成器",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded",
        )

    @staticmethod
    def apply_custom_css():
        """应用自定义CSS样式"""
        st.markdown(
            """
        <style>
            .main-header {
                font-size: 2.5rem;
                font-weight: bold;
                color: #1f77b4;
                text-align: center;
                margin-top: -2rem;
                margin-bottom: 2rem;
            }
            .section-header {
                font-size: 1.5rem;
                font-weight: bold;
                color: #2e8b57;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }
        </style>
        """,
            unsafe_allow_html=True,
        )

    @staticmethod
    def render_header():
        """渲染页面标题"""
        st.markdown(
            '<div class="main-header">📊 BSA 数据生成器</div>', unsafe_allow_html=True
        )

    @staticmethod
    def _get_cookie_unified(server_address: str | None = None, show_ui_feedback: bool = True, silent_mode: bool = False):
        """统一的Cookie获取方法（UI层实现，处理配置和用户交互）

        Args:
            server_address: 服务器地址，如果为None则从配置文件读取
            show_ui_feedback: 是否显示UI反馈（成功/错误消息）
            silent_mode: 是否静默模式（不显示任何UI消息）
        """
        try:
            # 获取当前配置
            config = BSAConfigManager.load_config()

            # 确定服务器地址
            if server_address is None:
                server_address = config.get('server_address', '*************:8002')

            # 提取域名
            domain = BSACookieManager.extract_domain_from_server_address(server_address or '*************:8002')

            # 获取Cookie（调用核心业务逻辑）
            cookie_string = BSACookieManager.get_chrome_cookies(domain)

            if cookie_string:
                # 更新配置
                config['cookie'] = cookie_string
                BSAConfigManager.save_config(config)

                # 更新session state中的配置
                if 'saved_config' in st.session_state:
                    st.session_state.saved_config = BSAConfigManager.load_config()

                # 显示成功消息
                if show_ui_feedback and not silent_mode:
                    st.sidebar.success("🔄 已自动更新Cookie")

                return True, "Cookie获取成功"
            else:
                error_msg = f"未找到域名 '{domain}' 的Cookie，请确保已在Chrome中登录该网站"
                if show_ui_feedback and not silent_mode:
                    st.warning(f"⚠️ {error_msg}")
                return False, error_msg

        except Exception as e:
            error_msg = str(e)
            if show_ui_feedback and not silent_mode:
                st.error(f"❌ Cookie获取失败：{str(e)}")
            return False, error_msg



    @staticmethod
    def render_sidebar_config():
        """渲染侧边栏配置"""
        st.sidebar.markdown("## 🔧 参数配置")

        # 初始化session state中的配置（只在首次加载时从文件读取）
        if 'config_loaded' not in st.session_state:
            st.session_state.config_loaded = True
            st.session_state.saved_config = BSAConfigManager.load_config()

            # 程序启动时自动获取Cookie
            BSAUserInterface._get_cookie_unified(
                server_address=None,
                show_ui_feedback=False,  # 自动获取时不显示UI反馈
                silent_mode=True  # 静默模式，避免干扰用户体验
            )

            # 重新加载配置以获取更新后的Cookie
            st.session_state.saved_config = BSAConfigManager.load_config()

        # 使用session state中的配置
        saved_config = st.session_state.saved_config

        # 测试ID配置
        test_id = st.sidebar.number_input(
            "**测试ID**",
            min_value=1,
            max_value=9999,
            value=saved_config.get('test_id', 400),
            help="测试的唯一标识符",
            key="config_test_id",
            on_change=lambda: BSAUserInterface._update_config('test_id', st.session_state.config_test_id)
        )

        # 数据配置
        st.sidebar.markdown("### 数据配置")
        with st.sidebar.expander("📊 数据生成配置", expanded=True):
            
            # 时间设置
            st.markdown("**⏰ 时间设置**")
            col1, col2 = st.columns(2)
            with col1:
                saved_date = datetime.strptime(saved_config.get('start_date', '2025-01-01'), '%Y-%m-%d').date()
                start_date = st.date_input(
                    "起始日期",
                    value=saved_date,
                    help="数据生成的起始日期",
                    key="config_start_date",
                    on_change=lambda: BSAUserInterface._update_config('start_date', st.session_state.config_start_date.strftime('%Y-%m-%d'))
                )
            with col2:
                saved_time = datetime.strptime(saved_config.get('start_time', '08:00:00'), '%H:%M:%S').time()
                start_time = st.time_input(
                    "起始时间",
                    value=saved_time,
                    help="数据生成的起始时间",
                    key="config_start_time",
                    on_change=lambda: BSAUserInterface._update_config('start_time', st.session_state.config_start_time.strftime('%H:%M:%S'))
                )

            # 间隔设置
            col1, col2 = st.columns(2)
            with col1:
                interval_value = st.number_input(
                    "时间间隔",
                    min_value=1,
                    max_value=1000,
                    value=saved_config.get('interval_value', 1),
                    help="时间间隔的数值",
                    key="config_interval_value",
                    on_change=lambda: BSAUserInterface._update_config('interval_value', st.session_state.config_interval_value)
                )
            with col2:
                interval_options = ["minute", "second", "hour", "day", "month", "year"]
                saved_interval_type = saved_config.get('interval_type', 'minute')
                interval_index = interval_options.index(saved_interval_type) if saved_interval_type in interval_options else 0

                interval_type = st.selectbox(
                    "间隔单位",
                    options=interval_options,
                    format_func=lambda x: {
                        "minute": "分钟",
                        "second": "秒",
                        "hour": "小时",
                        "day": "天",
                        "month": "月",
                        "year": "年",
                    }[x],
                    index=interval_index,
                    help="时间间隔的单位",
                    key="config_interval_type",
                    on_change=lambda: BSAUserInterface._update_config('interval_type', st.session_state.config_interval_type)
                )

            st.markdown("---")  # 分隔线
            
            # 数据设置
            st.markdown("**🎲 数据设置**")
            
            # 记录数和子组大小
            col1, col2 = st.columns(2)
            with col1:
                total_count = st.number_input(
                    "数据行数",
                    min_value=1,
                    max_value=10000,
                    value=saved_config.get('total_count', 100),
                    help="要生成的数据记录总数",
                    key="config_total_count",
                    on_change=lambda: BSAUserInterface._update_config('total_count', st.session_state.config_total_count)
                )
            with col2:
                size = st.number_input(
                    "子组大小",
                    min_value=1,
                    max_value=25,
                    value=saved_config.get('size', 2),
                    help="每条记录中的测量子组大小 (v1-v25)",
                    key="config_size",
                    on_change=lambda: BSAUserInterface._update_config('size', st.session_state.config_size)
                )

            # 数据类型和数据随机
            col1, col2 = st.columns(2)
            with col1:
                data_type_options = ["整数", "浮点数"]
                saved_data_type = saved_config.get('data_type', '整数')
                data_type_index = data_type_options.index(saved_data_type) if saved_data_type in data_type_options else 0

                data_type = st.radio(
                    "数据类型",
                    options=data_type_options,
                    index=data_type_index,
                    help="选择生成数据的类型：整数或浮点数",
                    key="config_data_type",
                    on_change=lambda: BSAUserInterface._update_config('data_type', st.session_state.config_data_type),
                    horizontal=True
                )
            with col2:
                seed_mode_options = ["随机", "固定"]
                saved_seed_mode = "固定" if saved_config.get('use_fixed_seed', False) else "随机"
                seed_mode_index = seed_mode_options.index(saved_seed_mode) if saved_seed_mode in seed_mode_options else 0

                seed_mode = st.radio(
                    "数据随机",
                    options=seed_mode_options,
                    index=seed_mode_index,
                    help="选择随机种子模式：随机每次生成不同数据，固定使用指定种子值",
                    key="config_seed_mode",
                    on_change=lambda: BSAUserInterface._update_config('use_fixed_seed', st.session_state.config_seed_mode == "固定"),
                    horizontal=True
                )

            # 设置use_fixed_seed变量
            use_fixed_seed = (seed_mode == "固定")
            
            # 数据值范围
            st.markdown("**📏 数据值范围**")
            col1, col2 = st.columns(2)
            
            if data_type == "整数":
                # 整数类型：只允许输入整数
                with col1:
                    saved_min = int(float(saved_config.get('min_value', '-100')))
                    min_value = st.number_input(
                        "最小值",
                        value=saved_min,
                        step=1,
                        help="生成数据的最小值（整数）",
                        key="config_min_value_int",
                        on_change=lambda: BSAUserInterface._update_config('min_value', str(st.session_state.config_min_value_int))
                    )
                with col2:
                    saved_max = int(float(saved_config.get('max_value', '100')))
                    max_value = st.number_input(
                        "最大值",
                        value=saved_max,
                        step=1,
                        help="生成数据的最大值（整数）",
                        key="config_max_value_int",
                        on_change=lambda: BSAUserInterface._update_config('max_value', str(st.session_state.config_max_value_int))
                    )
            else:
                # 浮点数类型：使用文本输入框以支持任意精度
                with col1:
                    min_value_str = st.text_input(
                        "最小值",
                        value=saved_config.get('min_value', '-100'),
                        help="生成数据的最小值（支持高精度小数，如: -100 或 -100.123456）",
                        key="config_min_value_float",
                        on_change=lambda: BSAUserInterface._update_config('min_value', st.session_state.config_min_value_float)
                    )
                    try:
                        min_value = float(min_value_str or '-100')
                    except ValueError:
                        st.error("❌ 最小值必须是有效的数字")
                        min_value = -100.0

                with col2:
                    max_value_str = st.text_input(
                        "最大值",
                        value=saved_config.get('max_value', '100'),
                        help="生成数据的最大值（支持高精度小数，如: 100 或 100.123456",
                        key="config_max_value_float",
                        on_change=lambda: BSAUserInterface._update_config('max_value', st.session_state.config_max_value_float)
                    )
                    try:
                        max_value = float(max_value_str or '100')
                    except ValueError:
                        st.error("❌ 最大值必须是有效的数字")
                        max_value = 100.0

                # 精度验证警告
                if (
                    isinstance(min_value, float) and len(str(min_value).split(".")[-1]) > 16
                ) or (
                    isinstance(max_value, float) and len(str(max_value).split(".")[-1]) > 16
                ):
                    st.warning("⚠️ 不支持超过16位小数的精度")

            # 设置均值、标准差
            mean_col, std_col = st.columns(2)
            # 均值设置
            with mean_col:
                mean_str = st.text_input(
                    "均值",
                    value=saved_config.get('mean', ''),
                    key="config_mean",
                    on_change=lambda: BSAUserInterface._update_config('mean', st.session_state.config_mean if st.session_state.config_mean and st.session_state.config_mean.strip() else '')
                )
                try:
                    mean_value = float(mean_str) if mean_str and mean_str.strip() else None
                except ValueError:
                    if mean_str and mean_str.strip():
                        st.error("❌ 均值必须是有效的数字")
                    mean_value = None

            # 标准差设置
            with std_col:
                stddev_str = st.text_input(
                    "标准差",
                    value=saved_config.get('stddev', ''),
                    key="config_stddev",
                    on_change=lambda: BSAUserInterface._update_config('stddev', st.session_state.config_stddev if st.session_state.config_stddev and st.session_state.config_stddev.strip() else '')
                )
                try:
                    if stddev_str and stddev_str.strip():
                        stddev_value = float(stddev_str)
                        if stddev_value <= 0:
                            st.error("❌ 标准差必须大于0")
                            stddev_value = None
                    else:
                        stddev_value = None
                except ValueError:
                    stddev_value = None
                    if stddev_str and stddev_str.strip():
                        st.error("❌ 标准差必须是有效的数字")

            # 随机种子配置（对所有分布类型都显示）
            st.markdown("**🎲 随机种子**")
            random_seed = st.number_input(
                "随机种子",
                min_value=0,
                max_value=999999,
                value=saved_config.get('random_seed', 12345),
                help="用于生成随机数的种子值，相同种子产生相同随机序列",
                key="config_random_seed",
                on_change=lambda: BSAUserInterface._update_config('random_seed', st.session_state.config_random_seed),
                disabled=(seed_mode == "随机")
            )

            # 如果是数据随机，则random_seed设为None
            if seed_mode == "随机":
                random_seed = None

        # 合并日期和时间
        start_datetime_str = f"{start_date.strftime('%Y-%m-%d')} {start_time.strftime('%H:%M:%S')}"

        # 字段设置
        st.sidebar.markdown("#### 字段设置")
        with st.sidebar.expander("📝 元数据字段配置", expanded=False):
            st.markdown("**配置 f1-f9 字段内容**")
            # f1-f9 字段配置
            field_configs = [
                ("f1", "过程变量 1", "如: 过程-01"),
                ("f2", "过程变量 2", "如: 操作员-02"),
                ("f3", "过程变量 3", "如: 产品型号-03"),
                ("f4", "过程变量 4", "如: 产品-04"),
                ("f5", "过程变量 5", "如: 设备-05"),
                ("f6", "过程变量 6", "如: 供应商-06"),
                ("f7", "过程变量 7", "如: 客户-07"),
                ("f8", "过程变量 8", "如: 批次-08"),
                ("f9", "过程变量 9", "如: 工单编号-09")
            ]
            
            field_values = {}
            for field_key, field_name, field_help in field_configs:
                field_values[field_key] = st.text_input(
                    f"{field_name} ({field_key})",
                    value=saved_config.get(field_key, ''),
                    help=field_help,
                    key=f"config_{field_key}",
                    on_change=lambda k=field_key: BSAUserInterface._update_config(k, st.session_state[f"config_{k}"])
                )

        # API配置
        st.sidebar.markdown("### API设置")
        with st.sidebar.expander("🔧 API接口配置", expanded=False):
            server_address = st.text_input(
                "服务器地址",
                value=saved_config.get('server_address', '*************:8002'),
                help="服务器地址和端口，如：*************:8002",
                key="config_server_address",
                on_change=lambda: BSAUserInterface._update_config('server_address', st.session_state.config_server_address)
            )

            # Cookie配置区域
            if st.button(
                "获取Cookie",
                help="从Chrome浏览器自动获取Cookie（需要先在Chrome中登录目标网站）",
                key="auto_get_cookie",
                # use_container_width=True
            ):
                with st.spinner("正在从Chrome浏览器获取Cookie..."):
                    # 调用统一的Cookie获取方法
                    BSAUserInterface._get_cookie_unified(
                        server_address=server_address,
                        show_ui_feedback=True,  # 手动获取时显示UI反馈
                        silent_mode=False  # 非静默模式，显示详细消息
                    )
                    # 刷新页面以显示更新后的Cookie
                    st.rerun()

            cookie = st.text_area(
                "Cookie",
                value=saved_config.get('cookie', ''),
                help="API请求的Cookie，可手动输入或点击按钮自动获取",
                key="config_cookie",
                on_change=lambda: BSAUserInterface._update_config('cookie', st.session_state.config_cookie),
                height=200
            )

        # 合并日期和时间
        start_datetime_str = f"{start_date.strftime('%Y-%m-%d')} {start_time.strftime('%H:%M:%S')}"

        return {
            "test_id": test_id,
            "start_time": start_datetime_str,
            "interval_value": interval_value,
            "interval_type": interval_type,
            "total_count": total_count,
            "size": size,
            "data_type": data_type,
            "min_value": min_value,
            "max_value": max_value,
            "mean": mean_value,
            "stddev": stddev_value,
            "use_fixed_seed": use_fixed_seed,
            "random_seed": random_seed,
            "server_address": server_address,
            "cookie": cookie,
            "f1": field_values.get("f1", ""),
            "f2": field_values.get("f2", ""),
            "f3": field_values.get("f3", ""),
            "f4": field_values.get("f4", ""),
            "f5": field_values.get("f5", ""),
            "f6": field_values.get("f6", ""),
            "f7": field_values.get("f7", ""),
            "f8": field_values.get("f8", ""),
            "f9": field_values.get("f9", ""),
        }

    @staticmethod
    def render_action_buttons(config):
        """渲染三个并排操作按钮"""
        col1, col2, col3 = st.columns(3)

        # 生成数据按钮
        with col1:
            generate_clicked = st.button(
                "🚀 生成数据", type="primary", use_container_width=True
            )

        # 下载文件按钮
        with col2:
            download_clicked = st.button(
                "📥 下载文件",
                disabled="full_df" not in st.session_state,
                use_container_width=True,
                help="请先生成数据"
                if "full_df" not in st.session_state
                else "选择格式并下载文件",
            )

        # API请求按钮
        with col3:
            # 检查API按钮是否应该启用
            api_enabled = "full_df" in st.session_state and config["cookie"].strip()

            if "full_df" not in st.session_state:
                help_text = "请先生成数据"
            elif not config["cookie"].strip():
                help_text = "请在侧边栏配置Cookie后使用此功能"
            else:
                help_text = "发送数据到API服务器"

            api_clicked = st.button(
                "📤 发送API请求",
                disabled=not api_enabled,
                use_container_width=True,
                help=help_text,
            )

        return generate_clicked, download_clicked, api_clicked

    @staticmethod
    def handle_download_dialog():
        """处理下载对话框"""

        @st.dialog("选择下载格式")
        def download_dialog():
            st.markdown("**请选择文件格式：**")

            # 文件格式选择
            file_format = st.radio(
                "格式",
                options=["xlsx", "csv", "xls", "json"],
                format_func=lambda x: {
                    "xlsx": "Excel 2007+ (.xlsx)",
                    "csv": "逗号分隔值 (.csv)",
                    "xls": "Excel 97-2003 (.xls)",
                    "json": "JSON 数据 (.json)",
                }[x],
                index=0,
            )

            # 生成文件名
            download_filename = (
                f"BSA-{st.session_state.config['test_id']}.{file_format}"
            )

            # 创建下载数据
            try:
                if file_format == "json":
                    download_data = BSAExportClient.create_file_data(
                        st.session_state.full_df,
                        file_format,
                        st.session_state.json_data,
                    )
                else:
                    download_data = BSAExportClient.create_file_data(
                        st.session_state.full_df, file_format
                    )

                # 直接显示下载按钮，点击即可下载
                mime_types = {
                    "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "xls": "application/vnd.ms-excel",
                    "csv": "text/csv",
                    "json": "application/json",
                }
                st.download_button(
                    label=f"确认下载 {file_format.upper()} 文件",
                    data=download_data,
                    file_name=download_filename,
                    mime=mime_types.get(file_format, "application/octet-stream"),
                    type="primary",
                    use_container_width=True,
                )

            except Exception as e:
                st.error(f"文件生成失败: {str(e)}")

        download_dialog()

    @staticmethod
    def render_data_preview_and_results():
        """渲染数据预览和输出信息结果"""
        # 使用左右分栏布局
        preview_col, result_col = st.columns([2, 1])

        with preview_col:
            st.markdown("**📊 数据预览**")

            # 根据是否有数据显示不同内容
            if "full_df" in st.session_state:
                st.dataframe(st.session_state.full_df, use_container_width=True)
                # 显示数据统计
                st.markdown("**📊 数据统计：**")
                stats_col1, stats_col2, stats_col3 = st.columns(3)
                with stats_col1:
                    st.metric("总行数", len(st.session_state.full_df))
                with stats_col2:
                    st.metric("总列数", len(st.session_state.full_df.columns))
                with stats_col3:
                    st.metric(
                        "数据大小",
                        f"{st.session_state.full_df.memory_usage(deep=True).sum() / 1024:.1f} KB",
                    )
            else:
                # 显示空的数据框架
                empty_df = pd.DataFrame()
                st.dataframe(empty_df, use_container_width=True)

                # 显示空的统计信息
                st.markdown("**📊 数据统计：**")
                stats_col1, stats_col2, stats_col3 = st.columns(3)
                with stats_col1:
                    st.metric("总行数", 0)
                with stats_col2:
                    st.metric("总列数", 0)
                with stats_col3:
                    st.metric("数据大小", "0 KB")

        with result_col:
            # 创建一个容器来统一样式
            info_container = st.container()

            with info_container:
                # 数据生成信息区域
                st.markdown("##### 🚀 生成结果")
                if "generation_info" in st.session_state:
                    st.info(st.session_state.generation_info)
                else:
                    st.warning("💡 暂未生成数据")

                # 分隔线
                st.markdown("---")

                # API请求信息区域
                st.markdown("##### 📤 API请求结果")
                if "api_info" in st.session_state:
                    if st.session_state.get("api_success", False):
                        st.success(st.session_state.api_info)
                        # 显示详细响应结果
                        with st.expander("查看详细响应", expanded=True):
                            st.json(st.session_state.api_result)
                    else:
                        st.error(st.session_state.api_info)
                        # 显示错误详情
                        with st.expander("查看错误详情"):
                            st.error(st.session_state.api_result)
                else:
                    # 检查API请求条件
                    if "full_df" not in st.session_state:
                        st.info("💡 请先生成数据")
                    else:
                        cookie = st.session_state.get("config", {}).get("cookie", "")
                        if not cookie.strip():
                            st.info("💡 配置Cookie后可发送API请求")
                        else:
                            st.warning("💡 暂未发送请求")

    @staticmethod
    def render_footer():
        """渲染页脚"""
        st.markdown("---")
        st.markdown(
            "<div style='text-align: center; color: #666; font-size: 0.9rem;'>"
            "BSA 数据生成器 - 基于 Streamlit 构建 🚀"
            "</div>",
            unsafe_allow_html=True,
        )


def main():
    """主应用函数"""
    # 设置页面配置
    BSAUserInterface.setup_page_config()

    # 应用自定义CSS
    BSAUserInterface.apply_custom_css()

    # 渲染页面标题
    BSAUserInterface.render_header()

    # 渲染侧边栏配置并获取配置参数
    config = BSAUserInterface.render_sidebar_config()

    # 参数验证
    is_valid, error_msg = BSADataEngine.validate_config(config)
    if not is_valid:
        st.error(f"❌ {error_msg}")
        return

    # 渲染操作按钮
    generate_clicked, download_clicked, api_clicked = (
        BSAUserInterface.render_action_buttons(config)
    )

    # 处理生成数据按钮点击
    if generate_clicked:
        with st.spinner("正在生成数据..."):
            try:
                # 创建数据引擎并生成数据
                engine = BSADataEngine(config)
                full_df, json_data = engine.generate_complete_dataset()

                # 存储到session state
                st.session_state.full_df = full_df
                st.session_state.json_data = json_data
                st.session_state.config = config
                
                # 存储数据生成信息 - 合并为一个变量
                st.session_state.generation_info = f"✅ 数据生成成功！生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                # 清除之前的API响应结果
                if "api_result" in st.session_state:
                    del st.session_state.api_result
                if "api_success" in st.session_state:
                    del st.session_state.api_success
                if "api_info" in st.session_state:
                    del st.session_state.api_info

                st.success(f"✅ 数据生成成功！共生成 {len(full_df)} 条记录。")

                # 强制重新运行以更新按钮状态
                st.rerun()

            except Exception as e:
                st.error(f"❌ 数据生成失败: {str(e)}")

    # 处理下载文件按钮点击
    if "full_df" in st.session_state and download_clicked:
        BSAUserInterface.handle_download_dialog()

    # 处理API请求按钮点击
    if api_clicked and "full_df" in st.session_state and config["cookie"].strip():
        with st.spinner("正在发送API请求..."):
            try:
                # 发送API请求
                api_result = BSAExportClient.send_api_request(
                    st.session_state.json_data, config["server_address"], config["cookie"]
                )

                # 存储API响应结果到session state
                api_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                st.session_state.api_result = api_result
                st.session_state.api_success = True
                st.session_state.api_info = f"✅ API请求成功！发送时间: {api_time}"

            except Exception as e:
                api_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                st.session_state.api_result = str(e)
                st.session_state.api_success = False
                st.session_state.api_info = f"❌ API请求失败！发送时间: {api_time}"

    # 渲染数据预览和API响应结果
    BSAUserInterface.render_data_preview_and_results()

    # 渲染页脚
    BSAUserInterface.render_footer()


if __name__ == "__main__":
    main()
